{"name": "social-media-automation", "version": "1.1.0", "description": "Automated Instagram/Facebook responses using Node.js, Express, Google Gemini, and MongoDB", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "migrate": "node utils/migrateToMongoDB.js", "watch": "nodemon --watch ./ --ignore ./.data/ server.js"}, "dependencies": {"axios": "^1.6.2", "bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "dotenv": "^16.3.1", "express": "^4.18.2", "express-session": "^1.17.3", "fs-extra": "^11.1.1", "jsonwebtoken": "^9.0.2", "moment": "^2.29.4", "mongoose": "^8.15.0", "uuid": "^9.0.1"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": "18"}}