# Server Configuration
PORT=3000
NODE_ENV=production
SESSION_SECRET=your-session-secret

# Facebook/Instagram OAuth
FACEBOOK_APP_ID=your-facebook-app-id
FACEBOOK_APP_SECRET=your-facebook-app-secret
# يجب تحديث هذا العنوان بعنوان الخدمة الخاصة بك
# للنشر على Render.com
# REDIRECT_URI=https://your-app-name.onrender.com/api/oauth/callback
# للنشر على Glitch.com
# REDIRECT_URI=https://your-project-name.glitch.me/api/oauth/callback
# للنشر على Cyclic.sh
# REDIRECT_URI=https://your-app-name.cyclic.app/api/oauth/callback
FACEBOOK_VERIFY_TOKEN=your-webhook-verify-token

# Google Gemini API
GEMINI_API_KEY=your-gemini-api-key

# Admin Credentials
ADMIN_USERNAME=admin
ADMIN_PASSWORD=secure-password

# MongoDB Configuration
MONGODB_URI=mongodb+srv://<username>:<password>@cluster0.mongodb.net/social-media-automation?retryWrites=true&w=majority
